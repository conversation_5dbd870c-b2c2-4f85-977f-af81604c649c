import random
from datetime import datetime
from zoneinfo import ZoneInfo

from agents.agent_conf import AgentInfo
from agents.language_settings import LanguageSettings


class AgentSettings:
    languages = {}
    response_style: str = """
   Always respond using these guidelines. 
   ### Important: Keep responses short (max 3 sentences), conversational, and emotional.
   Always add "--" between the sentences.  

   **Response Guidelines:**
   **Your Punctuation**
   - **Chat Smiles** :-), ;-) :) :( to express emotions
   - **Periods (`.`):** End sentences with a full stop and slight pause.
   - **Commas (`,-`):** Insert brief pauses within sentences.
   - **Ellipses (`...`):** Create longer pauses or indicate trailing off.
   - **Single Dash (`-`):** Indicate a quick pause or change in thought.
   - **Double Dash (`--`):** Create a more pronounced pause.
   - **Triple Dash (`---`):** Emphasize a significant pause or interruption.
   - **Exclamation Marks (`!`):** Convey excitement or strong emotion.
   - **Question Marks (`?`):** Indicate a question, raising intonation.
   - **Repeated Punctuation:** Amplify emotion or intensity.
   - **Parentheses (`()`):** Add asides or additional information.
   **Way to Express Yourself:**
   - **Capitalization for Emphasis**
   - **Interjections and Colloquial Language**
   - **Informal Pronouns and Contractions**
   - **Mix Sentence Lengths for Rhythm**
   - **Repetition for Emphasis**

**Your Filler Words Usage:**
   - **Frequency:** Use filler words **sparingly**, approximately **once every 2-3 sentences**. Avoid overusing them to maintain professionalism.
   - **Examples of Filler Words:**
       - **Interjections:** "Hmm", "Um", "Uh", "Well", "You know", "Let's see", "I mean", "Like", "Actually", "So"

**Remember:**
- Keep the conversation **engaging** and **customer-focused**.
- Use punctuation to simulate slow talking and natural speech patterns.
- Insert reasonable pauses to control the rhythm and flow.  
    """
    tools_prompt = """
IMPORTANT: You are on a phone call.
Keep responses short (max 3 sentences), conversational, and emotional.
   - Do not mention chats; you are talking with voice.
   - When you receive function tool feedback, do not repeat it. Just quickly confirm that it's done in an easy and natural way.
   - When the user asks to end the call, use function calling and finish the conversation.
   - Any follow-up will be sent to the current WhatsApp number, which the agent knows. Do not confirm the number or any other details related to the follow-up.
   - "Do not call me again" should use function calling. Always provide a short answer and finish the conversation.
   - When the user wants to schedule a callback, you may suggest a date and time.
   """

    silence_phrases = {
        'en-US': [
            "Are you still there?",
            "Just let me know when you're ready.",
            "Take your time, I'm here.",
            "No rush, I'll be here when you're ready.",
            "Feel free to ask whenever you're ready.",
            "I’m here whenever you’re ready to continue.",
            "Don't worry, I’m still here.",
            "Whenever you're ready, I'm listening.",
            "I'm here if you need me.",
            "Take your time, there's no hurry.",
            "I'm ready when you are.",
            "Feel free to continue when you're ready."
        ],
        'ar-EG': [
            "هل ما زلت هناك؟",
            "أخبرني فقط عندما تكون جاهزًا.",
            "خذ وقتك، أنا هنا.",
            "لا عجلة، سأكون هنا عندما تكون جاهزًا.",
            "لا تتردد في السؤال عندما تكون مستعدًا.",
            "أنا هنا عندما تكون مستعدًا للاستمرار.",
            "لا تقلق، ما زلت هنا.",
            "عندما تكون جاهزًا، أنا أستمع.",
            "أنا هنا إذا احتجت إلي.",
            "خذ وقتك، لا عجلة.",
            "أنا جاهز عندما تكون أنت.",
            "لا تتردد في المتابعة عندما تكون مستعدًا."
        ]
    }

    ending_phrases = {
        'en-US': [
            "Ok... goodbye :-(",
            "Ok... Have a nice day!",
            "Ok... Bye-bye",
            "Take care!",
            "See you soon!",
            "Until next time."
        ],
        'ar-EG': [
            "حسنًا... وداعًا :-(",
            "حسنًا... أتمنى لك يومًا سعيدًا!",
            "حسنًا... إلى اللقاء",
            "اعتنِ بنفسك!",
            "أراك قريبًا!",
            "إلى المرة القادمة."
        ]
    }

    def __init__(self, agent_info: AgentInfo, profile_prompt=None, languages=None):
        self.config = agent_info
        self.voiceSettings = self.config.voice
        self.profile_prompt = profile_prompt
        self.lang_settings = LanguageSettings(languages)
        self.tools_prompt = self._build_tools_prompt()
        self.default_prompt = """"""


    def _build_tools_prompt(self) -> str:
        base_prompt = """
IMPORTANT:
Do not mention chats; you are talking with voice.
You are on a phone call.  You already say greeting to the user, do not repeat Greetings phrases anymore.
When you receive function tool feedback, do not repeat it.
Keep responses short (max 3 sentences), conversational, and emotional."""

        function_prompts = {
            "send_follow_up_message": """
   - When you receive function tool feedback, do not repeat it. Just quickly confirm that it's done in an easy and natural way.
   - Any follow-up will be sent to the current WhatsApp number, which the agent knows. Do not confirm the number or any other details related to the follow-up.
   - If the user is busy or not ready to talk right now, propose to send a follow-up or schedule next time.""",

            "schedule_callback": """
   - When the user wants to schedule a callback, you may suggest a date and time.""",

            "donot_call": """
   - "Do not call me again" should use function calling. Always provide a short answer and finish the conversation.
   - When the user asks to end the call, use function calling and finish the conversation."""
        }

        prompt_parts = [base_prompt]
        for function in self.config.functions:
            if function in function_prompts:
                prompt_parts.append(function_prompts[function])

        return "\n".join(prompt_parts)

    def get_time_update(self, timezone):
        now = datetime.now(ZoneInfo(timezone))
        weekday = now.weekday()
        return f"Current time: {now}  Weekday: {weekday} Time zone: {timezone}"

    def get_welcome_message(self, language=None):
        locale = self.lang_settings.get_locale(language)
        phrases = self.welcome_phrases.get(locale, self.welcome_phrases['en-US'])
        return random.choice(phrases)

    def get_ending_message(self, language=None):
        locale = self.lang_settings.get_locale(language)
        phrases = self.ending_phrases.get(locale, self.ending_phrases['en-US'])
        return random.choice(phrases)

    def get_silence_message(self, language=None) -> str:
        locale = self.lang_settings.get_locale(language)
        phrases = self.silence_phrases.get(locale, self.silence_phrases['en-US'])
        return random.choice(phrases)

    def get_system_prompt(self):
        language_instructions = self.lang_settings.get_language_instructions()
        return f'{self.profile_prompt} {self.config.mission.get_prompt(self.default_prompt)} {self.tools_prompt} {self.response_style} {language_instructions}'
