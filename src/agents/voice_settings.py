import json
from typing import Union, Dict, Any

from livekit.plugins import elevenlabs

from agents.language_settings import LanguageSettings
from config import get_config_value


class VoiceSettings:
    def __init__(self, voice_settings: Union[str, Dict[str, Any], None] = None):
        if isinstance(voice_settings, str):
            if voice_settings.strip() == '':
                voice_settings = {}
            else:
                try:
                    voice_settings = json.loads(voice_settings)
                except json.JSONDecodeError:
                    raise ValueError("voiceSettings is not valid JSON.")
        elif voice_settings is None:
            voice_settings = {}
        elif not isinstance(voice_settings, dict):
            raise TypeError("voiceSettings must be a dictionary, JSON string, or None.")

        # Get defaults from configuration
        default_provider = get_config_value('constants', 'voice_settings.default_provider', '11labs')
        default_stability = get_config_value('constants', 'voice_settings.default_stability', 0.8)
        default_similarity_boost = get_config_value('constants', 'voice_settings.default_similarity_boost', 0.8)
        default_style = get_config_value('constants', 'voice_settings.default_style', 0.6)
        default_use_speaker_boost = get_config_value('constants', 'voice_settings.default_use_speaker_boost', True)
        default_language = get_config_value('constants', 'voice_settings.default_language', 'eng')

        self.provider = voice_settings.get("provider", default_provider)
        self.stability = voice_settings.get("stability", default_stability)
        self.similarity_boost = voice_settings.get("similarity_boost", default_similarity_boost)
        self.style = voice_settings.get("style", default_style)
        self.use_speaker_boost = voice_settings.get("use_speaker_boost", default_use_speaker_boost)

        lang_settings = LanguageSettings(voice_settings.get("language", default_language))
        lang_name, _ = lang_settings.map_language_code(lang_settings.languages[0])
        self.language = lang_name

    def to_elevenlabs_voice_settings(self) -> elevenlabs.tts.VoiceSettings:
        return elevenlabs.tts.VoiceSettings(
            stability=self.stability,
            similarity_boost=self.similarity_boost,
            style=self.style,
            use_speaker_boost=self.use_speaker_boost,
        )
