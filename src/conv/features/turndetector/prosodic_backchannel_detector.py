import asyncio

from config import load_config, get_config_value
from .turn_feature_detector import TurnFeatureDetector


class ProsodicBackchannelDetector(TurnFeatureDetector):
	def __init__(self, ack_set=None, max_ack_words=2, energy_thresh=0.01, vad=None):
        if ack_set is None:
            messages_config = load_config('messages')
            self.ack_set = set(messages_config.get('acknowledgment_phrases', []))
        else:
            self.ack_set = ack_set

        self.max_ack_words = max_ack_words or get_config_value('constants', 'turn_detection.default_max_ack_words', 2)
        self.energy_thresh = energy_thresh or get_config_value('constants', 'turn_detection.default_energy_thresh',
                                                               0.01)
		self.vad = vad
	
	async def check(self, chat_ctx):
		texts = [m.text_content.lower().strip() for m in chat_ctx.items if
		         hasattr(m, "role") and m.role == "user" and m.text_content]
		if not texts:
			return False
		last = texts[-1]
		if len(last.split()) <= self.max_ack_words and last in self.ack_set:
			attr = getattr(chat_ctx, "last_audio_chunk", None)
			if attr is None:
				return False
			audio_chunk = attr() if callable(attr) else attr
			if audio_chunk is None:
				return False
			energy_var = await asyncio.to_thread(self.vad.compute_energy_variance, audio_chunk)
			if energy_var < self.energy_thresh:
				return True
		return False
