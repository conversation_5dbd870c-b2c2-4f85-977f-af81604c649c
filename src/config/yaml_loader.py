"""
YAML Configuration Loader

This module provides utilities for loading configuration from YAML files.
All application literals and configuration values should be externalized to YAML files
and loaded through this module.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path


class YAMLConfigLoader:
    """Loads and manages YAML configuration files."""
    
    def __init__(self, config_dir: str = "config"):
        """
        Initialize the YAML config loader.
        
        Args:
            config_dir: Directory containing YAML configuration files
        """
        self.config_dir = Path(config_dir)
        self._cache: Dict[str, Dict[str, Any]] = {}
        
    def load_config(self, filename: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        Load a YAML configuration file.
        
        Args:
            filename: Name of the YAML file (without extension)
            use_cache: Whether to use cached version if available
            
        Returns:
            Dictionary containing the configuration data
            
        Raises:
            FileNotFoundError: If the configuration file doesn't exist
            yaml.YAMLError: If the YAML file is malformed
        """
        if use_cache and filename in self._cache:
            return self._cache[filename]
            
        file_path = self.config_dir / f"{filename}.yaml"
        
        if not file_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
            
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file) or {}
                
            if use_cache:
                self._cache[filename] = config
                
            return config
            
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Error parsing YAML file {file_path}: {e}")
    
    def get_value(self, filename: str, key_path: str, default: Any = None) -> Any:
        """
        Get a specific value from a configuration file using dot notation.
        
        Args:
            filename: Name of the YAML file (without extension)
            key_path: Dot-separated path to the value (e.g., "database.host")
            default: Default value if key is not found
            
        Returns:
            The value at the specified path, or default if not found
        """
        config = self.load_config(filename)
        
        keys = key_path.split('.')
        value = config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
                
        return value
    
    def reload_config(self, filename: str) -> Dict[str, Any]:
        """
        Force reload a configuration file, bypassing cache.
        
        Args:
            filename: Name of the YAML file (without extension)
            
        Returns:
            Dictionary containing the reloaded configuration data
        """
        if filename in self._cache:
            del self._cache[filename]
        return self.load_config(filename, use_cache=False)
    
    def clear_cache(self):
        """Clear all cached configurations."""
        self._cache.clear()


# Global instance for easy access
_config_loader = YAMLConfigLoader()


def load_config(filename: str, use_cache: bool = True) -> Dict[str, Any]:
    """
    Convenience function to load a YAML configuration file.
    
    Args:
        filename: Name of the YAML file (without extension)
        use_cache: Whether to use cached version if available
        
    Returns:
        Dictionary containing the configuration data
    """
    return _config_loader.load_config(filename, use_cache)


def get_config_value(filename: str, key_path: str, default: Any = None) -> Any:
    """
    Convenience function to get a specific value from a configuration file.
    
    Args:
        filename: Name of the YAML file (without extension)
        key_path: Dot-separated path to the value (e.g., "database.host")
        default: Default value if key is not found
        
    Returns:
        The value at the specified path, or default if not found
    """
    return _config_loader.get_value(filename, key_path, default)


def reload_config(filename: str) -> Dict[str, Any]:
    """
    Convenience function to force reload a configuration file.
    
    Args:
        filename: Name of the YAML file (without extension)
        
    Returns:
        Dictionary containing the reloaded configuration data
    """
    return _config_loader.reload_config(filename)
