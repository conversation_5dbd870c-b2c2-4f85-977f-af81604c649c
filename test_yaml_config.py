#!/usr/bin/env python3
"""
Test script to verify YAML configuration loading works correctly.
"""

import sys
import os

# Add src to path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config import load_config, get_config_value

def test_yaml_loading():
    """Test that all YAML configuration files can be loaded."""
    print("Testing YAML configuration loading...")
    
    try:
        # Test loading each configuration file
        prompts_config = load_config('prompts')
        print("✓ Prompts config loaded successfully")
        
        messages_config = load_config('messages')
        print("✓ Messages config loaded successfully")
        
        language_config = load_config('language_mappings')
        print("✓ Language mappings config loaded successfully")
        
        constants_config = load_config('constants')
        print("✓ Constants config loaded successfully")
        
        # Test specific value retrieval
        response_style = get_config_value('prompts', 'response_style')
        if response_style:
            print("✓ Response style retrieved successfully")
        else:
            print("✗ Failed to retrieve response style")
            
        silence_phrases = get_config_value('messages', 'silence_phrases.en-US')
        if silence_phrases:
            print(f"✓ Silence phrases retrieved successfully ({len(silence_phrases)} phrases)")
        else:
            print("✗ Failed to retrieve silence phrases")
            
        ack_phrases = get_config_value('messages', 'acknowledgment_phrases')
        if ack_phrases:
            print(f"✓ Acknowledgment phrases retrieved successfully ({len(ack_phrases)} phrases)")
        else:
            print("✗ Failed to retrieve acknowledgment phrases")
            
        lang_mappings = get_config_value('language_mappings', 'language_codes_to_names')
        if lang_mappings:
            print(f"✓ Language mappings retrieved successfully ({len(lang_mappings)} languages)")
        else:
            print("✗ Failed to retrieve language mappings")
            
        sentiment_labels = get_config_value('constants', 'sentiment_labels')
        if sentiment_labels:
            print(f"✓ Sentiment labels retrieved successfully ({len(sentiment_labels)} labels)")
        else:
            print("✗ Failed to retrieve sentiment labels")
            
        print("\n✅ All YAML configuration tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ YAML configuration test failed: {e}")
        return False

def test_agent_settings():
    """Test that AgentSettings can be instantiated with YAML config."""
    print("\nTesting AgentSettings with YAML config...")
    
    try:
        from agents.agent_conf import AgentInfo, Mission
        from agents.settings import AgentSettings
        from conv.conv_meta import VoiceSettings
        
        # Create a mock agent info
        voice_settings = VoiceSettings()
        mission = Mission(
            humanName="Test Agent",
            intro="Test intro",
            goal="Test goal"
        )
        
        agent_info = AgentInfo(
            name="Test Agent",
            voice=voice_settings,
            mission=mission,
            functions=["schedule_callback", "donot_call"]
        )
        
        # Create AgentSettings instance
        settings = AgentSettings(agent_info)
        
        # Test that properties work
        response_style = settings.response_style
        if response_style:
            print("✓ AgentSettings response_style property works")
        else:
            print("✗ AgentSettings response_style property failed")
            
        silence_phrases = settings.silence_phrases
        if silence_phrases:
            print(f"✓ AgentSettings silence_phrases property works ({len(silence_phrases)} locales)")
        else:
            print("✗ AgentSettings silence_phrases property failed")
            
        silence_message = settings.get_silence_message()
        if silence_message:
            print(f"✓ AgentSettings get_silence_message() works: '{silence_message[:50]}...'")
        else:
            print("✗ AgentSettings get_silence_message() failed")
            
        print("✅ AgentSettings YAML integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ AgentSettings YAML integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = True
    success &= test_yaml_loading()
    success &= test_agent_settings()
    
    if success:
        print("\n🎉 All tests passed! YAML configuration is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the configuration.")
        sys.exit(1)
